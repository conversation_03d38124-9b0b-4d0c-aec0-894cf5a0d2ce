<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的播客 - 播客宇宙</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .podcast-card {
            transition: all 0.3s ease;
        }
        .podcast-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .status-bar {
            background: linear-gradient(to right, #000, #111);
        }
        .tab-indicator {
            transition: all 0.3s ease;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .episode-item {
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        .episode-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- iOS状态栏 -->
    <div class="status-bar text-white text-sm flex justify-between items-center px-4 py-1 h-11">
        <div class="flex items-center space-x-2">
            <span class="font-medium">9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="w-6 h-3 border border-white rounded-sm relative">
                <div class="w-4 h-1.5 bg-white rounded-sm absolute top-0.5 left-0.5"></div>
            </div>
        </div>
    </div>

    <!-- 顶部用户信息 -->
    <div class="bg-white px-4 py-6 border-b border-gray-200">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-4">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=center" 
                     alt="用户头像" class="w-16 h-16 rounded-full object-cover">
                <div>
                    <h1 class="text-xl font-bold text-gray-900">我的播客</h1>
                    <p class="text-gray-500 text-sm">管理你的订阅和收藏</p>
                </div>
            </div>
            <button class="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors">
                <i class="fas fa-cog text-gray-600"></i>
            </button>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-card rounded-2xl p-4 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-bold mb-1">本周听播客</h3>
                    <p class="text-sm opacity-90">5小时23分钟</p>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold">12</div>
                    <div class="text-xs opacity-80">已完成单集</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 标签导航 -->
    <div class="bg-white border-b border-gray-200 px-4 py-2">
        <div class="flex space-x-8 relative">
            <button class="tab-btn text-blue-500 font-semibold py-3 relative">
                订阅
                <div class="tab-indicator absolute bottom-0 left-0 right-0 h-0.5 bg-blue-500"></div>
            </button>
            <button class="tab-btn text-gray-500 py-3">收藏</button>
            <button class="tab-btn text-gray-500 py-3">历史</button>
            <button class="tab-btn text-gray-500 py-3">下载</button>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="pb-20 overflow-y-auto">
        <!-- 订阅内容 -->
        <div class="tab-content" id="subscription-content">
            <!-- 快速操作 -->
            <div class="px-4 py-4 bg-white border-b border-gray-100">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-bold text-gray-900">我的订阅 (8)</h2>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1.5 bg-gray-100 text-gray-600 rounded-lg text-sm">
                            <i class="fas fa-filter mr-1"></i>筛选
                        </button>
                        <button class="px-3 py-1.5 bg-blue-500 text-white rounded-lg text-sm">
                            <i class="fas fa-plus mr-1"></i>添加
                        </button>
                    </div>
                </div>
            </div>

            <!-- 订阅列表 -->
            <div class="px-4 py-4 space-y-4">
                <div class="bg-white rounded-xl p-4 podcast-card">
                    <div class="flex items-center space-x-4">
                        <img src="https://images.unsplash.com/photo-1557804506-669a67965ba0?w=60&h=60&fit=crop&crop=center" 
                             alt="播客封面" class="w-16 h-16 rounded-xl object-cover">
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 mb-1">科技前沿</h3>
                            <p class="text-gray-500 text-sm mb-2">张科技 · 科技资讯</p>
                            <div class="flex items-center space-x-4 text-xs text-gray-400">
                                <span><i class="fas fa-clock mr-1"></i>2天前更新</span>
                                <span><i class="fas fa-podcast mr-1"></i>52集</span>
                                <span class="px-2 py-1 bg-green-50 text-green-600 rounded">已订阅</span>
                            </div>
                        </div>
                        <div class="flex flex-col space-y-2">
                            <button class="p-2 rounded-full bg-blue-50 text-blue-500">
                                <i class="fas fa-play text-sm"></i>
                            </button>
                            <button class="p-2 rounded-full bg-gray-50 text-gray-500">
                                <i class="fas fa-ellipsis-h text-sm"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl p-4 podcast-card">
                    <div class="flex items-center space-x-4">
                        <img src="https://images.unsplash.com/photo-1516035069371-29a1b244cc32?w=60&h=60&fit=crop&crop=center" 
                             alt="播客封面" class="w-16 h-16 rounded-xl object-cover">
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 mb-1">心理学漫步</h3>
                            <p class="text-gray-500 text-sm mb-2">李心理 · 心理健康</p>
                            <div class="flex items-center space-x-4 text-xs text-gray-400">
                                <span><i class="fas fa-clock mr-1"></i>1周前更新</span>
                                <span><i class="fas fa-podcast mr-1"></i>38集</span>
                                <span class="px-2 py-1 bg-green-50 text-green-600 rounded">已订阅</span>
                            </div>
                        </div>
                        <div class="flex flex-col space-y-2">
                            <button class="p-2 rounded-full bg-blue-50 text-blue-500">
                                <i class="fas fa-play text-sm"></i>
                            </button>
                            <button class="p-2 rounded-full bg-gray-50 text-gray-500">
                                <i class="fas fa-ellipsis-h text-sm"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl p-4 podcast-card">
                    <div class="flex items-center space-x-4">
                        <img src="https://images.unsplash.com/photo-1551434678-e076c223a692?w=60&h=60&fit=crop&crop=center" 
                             alt="播客封面" class="w-16 h-16 rounded-xl object-cover">
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 mb-1">商业思维</h3>
                            <p class="text-gray-500 text-sm mb-2">王商业 · 商业洞察</p>
                            <div class="flex items-center space-x-4 text-xs text-gray-400">
                                <span><i class="fas fa-clock mr-1"></i>3天前更新</span>
                                <span><i class="fas fa-podcast mr-1"></i>75集</span>
                                <span class="px-2 py-1 bg-green-50 text-green-600 rounded">已订阅</span>
                            </div>
                        </div>
                        <div class="flex flex-col space-y-2">
                            <button class="p-2 rounded-full bg-blue-50 text-blue-500">
                                <i class="fas fa-play text-sm"></i>
                            </button>
                            <button class="p-2 rounded-full bg-gray-50 text-gray-500">
                                <i class="fas fa-ellipsis-h text-sm"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近更新 -->
            <div class="px-4 py-4">
                <h2 class="text-lg font-bold text-gray-900 mb-4">最近更新</h2>
                <div class="bg-white rounded-xl overflow-hidden">
                    <div class="episode-item p-4 flex items-center space-x-4">
                        <img src="https://images.unsplash.com/photo-1557804506-669a67965ba0?w=40&h=40&fit=crop&crop=center" 
                             alt="播客封面" class="w-10 h-10 rounded-lg object-cover">
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900 text-sm mb-1">人工智能的未来发展</h4>
                            <p class="text-gray-500 text-xs">科技前沿 · 35:42 · 2天前</p>
                        </div>
                        <button class="p-2 rounded-full bg-blue-50 text-blue-500">
                            <i class="fas fa-play text-xs"></i>
                        </button>
                    </div>
                    
                    <div class="episode-item p-4 flex items-center space-x-4">
                        <img src="https://images.unsplash.com/photo-1516035069371-29a1b244cc32?w=40&h=40&fit=crop&crop=center" 
                             alt="播客封面" class="w-10 h-10 rounded-lg object-cover">
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900 text-sm mb-1">如何管理焦虑情绪</h4>
                            <p class="text-gray-500 text-xs">心理学漫步 · 28:15 · 1周前</p>
                        </div>
                        <button class="p-2 rounded-full bg-gray-100 text-gray-500">
                            <i class="fas fa-download text-xs"></i>
                        </button>
                    </div>

                    <div class="episode-item p-4 flex items-center space-x-4">
                        <img src="https://images.unsplash.com/photo-1551434678-e076c223a692?w=40&h=40&fit=crop&crop=center" 
                             alt="播客封面" class="w-10 h-10 rounded-lg object-cover">
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900 text-sm mb-1">创业公司的估值逻辑</h4>
                            <p class="text-gray-500 text-xs">商业思维 · 42:33 · 3天前</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-12 bg-gray-200 rounded-full h-1">
                                <div class="bg-blue-500 h-1 rounded-full" style="width: 60%"></div>
                            </div>
                            <button class="p-2 rounded-full bg-blue-50 text-blue-500">
                                <i class="fas fa-play text-xs"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部Tab Bar -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
        <div class="flex justify-around items-center">
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-home text-lg"></i>
                <span class="text-xs">首页</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-compass text-lg"></i>
                <span class="text-xs">发现</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-play-circle text-lg"></i>
                <span class="text-xs">播放</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-blue-500">
                <i class="fas fa-bookmark text-lg"></i>
                <span class="text-xs font-medium">我的</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-user text-lg"></i>
                <span class="text-xs">个人</span>
            </button>
        </div>
    </div>

    <script>
        // 标签切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const tabBtns = document.querySelectorAll('.tab-btn');
            
            tabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有活动状态
                    tabBtns.forEach(b => {
                        b.classList.remove('text-blue-500', 'font-semibold');
                        b.classList.add('text-gray-500');
                        const indicator = b.querySelector('.tab-indicator');
                        if (indicator) {
                            indicator.remove();
                        }
                    });
                    
                    // 添加当前活动状态
                    this.classList.add('text-blue-500', 'font-semibold');
                    this.classList.remove('text-gray-500');
                    
                    // 添加指示器
                    const indicator = document.createElement('div');
                    indicator.className = 'tab-indicator absolute bottom-0 left-0 right-0 h-0.5 bg-blue-500';
                    this.appendChild(indicator);
                });
            });
        });
    </script>
</body>
</html>