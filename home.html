<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 播客宇宙</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 自定义样式 */
        .podcast-card {
            transition: all 0.3s ease;
        }
        .podcast-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .status-bar {
            background: linear-gradient(to right, #000, #111);
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- iOS状态栏 -->
    <div class="status-bar text-white text-sm flex justify-between items-center px-4 py-1 h-11">
        <div class="flex items-center space-x-2">
            <span class="font-medium">9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="w-6 h-3 border border-white rounded-sm relative">
                <div class="w-4 h-1.5 bg-white rounded-sm absolute top-0.5 left-0.5"></div>
            </div>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="bg-white border-b border-gray-200 px-4 py-4 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">播客宇宙</h1>
            <p class="text-sm text-gray-500">发现你爱听的声音</p>
        </div>
        <button class="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors">
            <i class="fas fa-search text-gray-600"></i>
        </button>
    </div>

    <!-- 主要内容区域 -->
    <div class="pb-20 overflow-y-auto">
        <!-- 推荐横幅 -->
        <div class="gradient-bg m-4 rounded-2xl p-6 text-white relative overflow-hidden">
            <div class="relative z-10">
                <h2 class="text-xl font-bold mb-2">今日推荐</h2>
                <p class="text-sm opacity-90 mb-4">精心为你挑选的优质内容</p>
                <div class="flex items-center space-x-3">
                    <img src="https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=60&h=60&fit=crop&crop=center" 
                         alt="推荐播客" class="w-12 h-12 rounded-xl object-cover">
                    <div>
                        <h3 class="font-semibold">技术漫谈</h3>
                        <p class="text-xs opacity-80">科技前沿 · 15分钟</p>
                    </div>
                </div>
            </div>
            <div class="absolute right-4 bottom-4 opacity-20">
                <i class="fas fa-headphones text-4xl"></i>
            </div>
        </div>

        <!-- 最近播放 -->
        <div class="px-4 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-bold text-gray-900">最近播放</h2>
                <button class="text-blue-500 text-sm font-medium">查看全部</button>
            </div>
            <div class="space-y-4">
                <div class="flex items-center space-x-4 p-3 bg-white rounded-xl podcast-card">
                    <img src="https://images.unsplash.com/photo-1589903308904-1010c2294adc?w=50&h=50&fit=crop&crop=center" 
                         alt="播客封面" class="w-12 h-12 rounded-lg object-cover">
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-900 text-sm">创业内幕</h3>
                        <p class="text-gray-500 text-xs">商业思维 · 32分钟</p>
                        <div class="w-full bg-gray-200 rounded-full h-1 mt-2">
                            <div class="bg-blue-500 h-1 rounded-full" style="width: 45%"></div>
                        </div>
                    </div>
                    <button class="p-2 rounded-full bg-blue-50 text-blue-500">
                        <i class="fas fa-play text-xs"></i>
                    </button>
                </div>
                <div class="flex items-center space-x-4 p-3 bg-white rounded-xl podcast-card">
                    <img src="https://images.unsplash.com/photo-1516035069371-29a1b244cc32?w=50&h=50&fit=crop&crop=center" 
                         alt="播客封面" class="w-12 h-12 rounded-lg object-cover">
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-900 text-sm">心理学漫步</h3>
                        <p class="text-gray-500 text-xs">心理健康 · 28分钟</p>
                        <div class="w-full bg-gray-200 rounded-full h-1 mt-2">
                            <div class="bg-blue-500 h-1 rounded-full" style="width: 75%"></div>
                        </div>
                    </div>
                    <button class="p-2 rounded-full bg-blue-50 text-blue-500">
                        <i class="fas fa-play text-xs"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 热门播客 -->
        <div class="px-4 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-bold text-gray-900">热门播客</h2>
                <button class="text-blue-500 text-sm font-medium">更多</button>
            </div>
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-white rounded-xl p-4 podcast-card">
                    <img src="https://images.unsplash.com/photo-1590602847861-f357a9332bbc?w=120&h=120&fit=crop&crop=center" 
                         alt="播客封面" class="w-full h-24 rounded-lg object-cover mb-3">
                    <h3 class="font-semibold text-gray-900 text-sm mb-1">设计思维</h3>
                    <p class="text-gray-500 text-xs mb-2">用户体验 · 12集</p>
                    <div class="flex items-center justify-between">
                        <span class="text-yellow-500 text-xs">
                            <i class="fas fa-star"></i> 4.8
                        </span>
                        <button class="text-blue-500 text-xs">
                            <i class="fas fa-plus"></i> 订阅
                        </button>
                    </div>
                </div>
                <div class="bg-white rounded-xl p-4 podcast-card">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=120&h=120&fit=crop&crop=center" 
                         alt="播客封面" class="w-full h-24 rounded-lg object-cover mb-3">
                    <h3 class="font-semibold text-gray-900 text-sm mb-1">科技前线</h3>
                    <p class="text-gray-500 text-xs mb-2">科技资讯 · 25集</p>
                    <div class="flex items-center justify-between">
                        <span class="text-yellow-500 text-xs">
                            <i class="fas fa-star"></i> 4.6
                        </span>
                        <button class="text-blue-500 text-xs">
                            <i class="fas fa-plus"></i> 订阅
                        </button>
                    </div>
                </div>
                <div class="bg-white rounded-xl p-4 podcast-card">
                    <img src="https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?w=120&h=120&fit=crop&crop=center" 
                         alt="播客封面" class="w-full h-24 rounded-lg object-cover mb-3">
                    <h3 class="font-semibold text-gray-900 text-sm mb-1">职场进阶</h3>
                    <p class="text-gray-500 text-xs mb-2">职业发展 · 18集</p>
                    <div class="flex items-center justify-between">
                        <span class="text-yellow-500 text-xs">
                            <i class="fas fa-star"></i> 4.7
                        </span>
                        <button class="text-blue-500 text-xs">
                            <i class="fas fa-plus"></i> 订阅
                        </button>
                    </div>
                </div>
                <div class="bg-white rounded-xl p-4 podcast-card">
                    <img src="https://images.unsplash.com/photo-1493612276216-ee3925520721?w=120&h=120&fit=crop&crop=center" 
                         alt="播客封面" class="w-full h-24 rounded-lg object-cover mb-3">
                    <h3 class="font-semibold text-gray-900 text-sm mb-1">音乐故事</h3>
                    <p class="text-gray-500 text-xs mb-2">音乐文化 · 30集</p>
                    <div class="flex items-center justify-between">
                        <span class="text-yellow-500 text-xs">
                            <i class="fas fa-star"></i> 4.9
                        </span>
                        <button class="text-blue-500 text-xs">
                            <i class="fas fa-plus"></i> 订阅
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部Tab Bar -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
        <div class="flex justify-around items-center">
            <button class="flex flex-col items-center space-y-1 text-blue-500">
                <i class="fas fa-home text-lg"></i>
                <span class="text-xs font-medium">首页</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-compass text-lg"></i>
                <span class="text-xs">发现</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-play-circle text-lg"></i>
                <span class="text-xs">播放</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-bookmark text-lg"></i>
                <span class="text-xs">我的</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-user text-lg"></i>
                <span class="text-xs">个人</span>
            </button>
        </div>
    </div>
</body>
</html>