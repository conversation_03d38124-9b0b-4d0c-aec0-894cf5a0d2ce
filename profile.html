<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人资料 - 播客宇宙</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-bar {
            background: linear-gradient(to right, #000, #111);
        }
        .setting-item {
            transition: all 0.3s ease;
        }
        .setting-item:hover {
            background-color: rgba(0,0,0,0.02);
        }
        .profile-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .toggle-switch {
            appearance: none;
            width: 50px;
            height: 28px;
            background: #e5e7eb;
            border-radius: 14px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .toggle-switch:checked {
            background: #3b82f6;
        }
        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .toggle-switch:checked::after {
            left: 24px;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- iOS状态栏 -->
    <div class="status-bar text-white text-sm flex justify-between items-center px-4 py-1 h-11">
        <div class="flex items-center space-x-2">
            <span class="font-medium">9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="w-6 h-3 border border-white rounded-sm relative">
                <div class="w-4 h-1.5 bg-white rounded-sm absolute top-0.5 left-0.5"></div>
            </div>
        </div>
    </div>

    <!-- 用户资料头部 -->
    <div class="profile-bg text-white relative overflow-hidden">
        <div class="p-6 pb-8">
            <div class="flex items-center space-x-4 mb-6">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=center" 
                     alt="用户头像" class="w-20 h-20 rounded-full object-cover border-4 border-white border-opacity-30">
                <div class="flex-1">
                    <h1 class="text-2xl font-bold mb-1">张小明</h1>
                    <p class="text-sm opacity-90 mb-2">播客爱好者</p>
                    <p class="text-xs opacity-80">已听播客 2,453 小时</p>
                </div>
                <button class="p-2 rounded-full bg-white bg-opacity-20 backdrop-blur-sm">
                    <i class="fas fa-edit text-white"></i>
                </button>
            </div>
            
            <!-- 统计信息 -->
            <div class="grid grid-cols-3 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold">156</div>
                    <div class="text-xs opacity-80">订阅播客</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold">2.4K</div>
                    <div class="text-xs opacity-80">收藏单集</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold">42</div>
                    <div class="text-xs opacity-80">听播客天数</div>
                </div>
            </div>
        </div>
        <div class="absolute right-4 top-4 opacity-10">
            <i class="fas fa-user-circle text-6xl"></i>
        </div>
    </div>

    <!-- 设置列表 -->
    <div class="pb-20 overflow-y-auto">
        <!-- 账户设置 -->
        <div class="bg-white mt-4 mx-4 rounded-2xl overflow-hidden">
            <div class="px-4 py-3 bg-gray-50 border-b border-gray-100">
                <h2 class="text-sm font-semibold text-gray-700">账户设置</h2>
            </div>
            
            <div class="setting-item flex items-center justify-between p-4 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-blue-500 text-sm"></i>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">个人信息</div>
                        <div class="text-xs text-gray-500">管理你的基本信息</div>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>

            <div class="setting-item flex items-center justify-between p-4 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-shield-alt text-green-500 text-sm"></i>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">隐私与安全</div>
                        <div class="text-xs text-gray-500">数据安全与隐私保护</div>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>

            <div class="setting-item flex items-center justify-between p-4">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-key text-purple-500 text-sm"></i>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">账户与密码</div>
                        <div class="text-xs text-gray-500">修改密码和绑定信息</div>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>

        <!-- 播放设置 -->
        <div class="bg-white mt-4 mx-4 rounded-2xl overflow-hidden">
            <div class="px-4 py-3 bg-gray-50 border-b border-gray-100">
                <h2 class="text-sm font-semibold text-gray-700">播放设置</h2>
            </div>
            
            <div class="setting-item flex items-center justify-between p-4 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-play text-orange-500 text-sm"></i>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">自动播放</div>
                        <div class="text-xs text-gray-500">单集结束后自动播放下一集</div>
                    </div>
                </div>
                <input type="checkbox" class="toggle-switch" checked>
            </div>

            <div class="setting-item flex items-center justify-between p-4 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-download text-red-500 text-sm"></i>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">仅WiFi下载</div>
                        <div class="text-xs text-gray-500">仅在连接WiFi时下载播客</div>
                    </div>
                </div>
                <input type="checkbox" class="toggle-switch" checked>
            </div>

            <div class="setting-item flex items-center justify-between p-4 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-volume-up text-indigo-500 text-sm"></i>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">音质设置</div>
                        <div class="text-xs text-gray-500">高品质 · 256kbps</div>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>

            <div class="setting-item flex items-center justify-between p-4">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-moon text-teal-500 text-sm"></i>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">睡眠定时器</div>
                        <div class="text-xs text-gray-500">设置播放自动停止时间</div>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>

        <!-- 通知设置 -->
        <div class="bg-white mt-4 mx-4 rounded-2xl overflow-hidden">
            <div class="px-4 py-3 bg-gray-50 border-b border-gray-100">
                <h2 class="text-sm font-semibold text-gray-700">通知设置</h2>
            </div>
            
            <div class="setting-item flex items-center justify-between p-4 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-bell text-yellow-500 text-sm"></i>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">新单集通知</div>
                        <div class="text-xs text-gray-500">订阅播客有新内容时通知</div>
                    </div>
                </div>
                <input type="checkbox" class="toggle-switch" checked>
            </div>

            <div class="setting-item flex items-center justify-between p-4 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-heart text-pink-500 text-sm"></i>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">推荐通知</div>
                        <div class="text-xs text-gray-500">根据喜好推荐新播客</div>
                    </div>
                </div>
                <input type="checkbox" class="toggle-switch">
            </div>

            <div class="setting-item flex items-center justify-between p-4">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-clock text-gray-500 text-sm"></i>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">勿扰时间</div>
                        <div class="text-xs text-gray-500">22:00 - 08:00</div>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>

        <!-- 其他设置 -->
        <div class="bg-white mt-4 mx-4 rounded-2xl overflow-hidden">
            <div class="px-4 py-3 bg-gray-50 border-b border-gray-100">
                <h2 class="text-sm font-semibold text-gray-700">其他</h2>
            </div>
            
            <div class="setting-item flex items-center justify-between p-4 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-question-circle text-blue-500 text-sm"></i>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">帮助与反馈</div>
                        <div class="text-xs text-gray-500">使用帮助和意见反馈</div>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>

            <div class="setting-item flex items-center justify-between p-4 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-star text-green-500 text-sm"></i>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">评价应用</div>
                        <div class="text-xs text-gray-500">在App Store中评价</div>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>

            <div class="setting-item flex items-center justify-between p-4 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-info-circle text-purple-500 text-sm"></i>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">关于应用</div>
                        <div class="text-xs text-gray-500">版本 2.1.0 (Build 2024.01)</div>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>

            <div class="setting-item flex items-center justify-between p-4">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-sign-out-alt text-red-500 text-sm"></i>
                    </div>
                    <div>
                        <div class="font-medium text-red-600">退出登录</div>
                        <div class="text-xs text-gray-500">退出当前账户</div>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>

        <!-- 版权信息 -->
        <div class="text-center py-8 px-4">
            <p class="text-xs text-gray-400 mb-2">播客宇宙 © 2024</p>
            <p class="text-xs text-gray-400">发现你爱听的声音</p>
        </div>
    </div>

    <!-- 底部Tab Bar -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
        <div class="flex justify-around items-center">
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-home text-lg"></i>
                <span class="text-xs">首页</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-compass text-lg"></i>
                <span class="text-xs">发现</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-play-circle text-lg"></i>
                <span class="text-xs">播放</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-bookmark text-lg"></i>
                <span class="text-xs">我的</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-blue-500">
                <i class="fas fa-user text-lg"></i>
                <span class="text-xs font-medium">个人</span>
            </button>
        </div>
    </div>
</body>
</html>