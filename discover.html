<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发现 - 播客宇宙</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .podcast-card {
            transition: all 0.3s ease;
        }
        .podcast-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .category-tag {
            transition: all 0.3s ease;
        }
        .category-tag.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .status-bar {
            background: linear-gradient(to right, #000, #111);
        }
        .search-bar {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- iOS状态栏 -->
    <div class="status-bar text-white text-sm flex justify-between items-center px-4 py-1 h-11">
        <div class="flex items-center space-x-2">
            <span class="font-medium">9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="w-6 h-3 border border-white rounded-sm relative">
                <div class="w-4 h-1.5 bg-white rounded-sm absolute top-0.5 left-0.5"></div>
            </div>
        </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar sticky top-11 z-10 p-4 border-b border-gray-100">
        <div class="relative">
            <input type="text" placeholder="搜索播客或主播..." 
                   class="w-full pl-10 pr-4 py-3 bg-gray-100 rounded-xl border-0 focus:ring-2 focus:ring-blue-500 focus:bg-white transition-all">
            <i class="fas fa-search absolute left-3 top-3.5 text-gray-400"></i>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="pb-20 overflow-y-auto">
        <!-- 分类标签 -->
        <div class="px-4 py-4">
            <h2 class="text-lg font-bold text-gray-900 mb-4">探索分类</h2>
            <div class="flex flex-wrap gap-2">
                <button class="category-tag active px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-700">
                    全部
                </button>
                <button class="category-tag px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-700">
                    科技
                </button>
                <button class="category-tag px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-700">
                    商业
                </button>
                <button class="category-tag px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-700">
                    教育
                </button>
                <button class="category-tag px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-700">
                    娱乐
                </button>
                <button class="category-tag px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-700">
                    健康
                </button>
                <button class="category-tag px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-700">
                    艺术
                </button>
                <button class="category-tag px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-700">
                    体育
                </button>
            </div>
        </div>

        <!-- 热门搜索 -->
        <div class="px-4 mb-6">
            <h2 class="text-lg font-bold text-gray-900 mb-4">热门搜索</h2>
            <div class="flex flex-wrap gap-3">
                <span class="px-3 py-2 bg-orange-50 text-orange-600 rounded-lg text-sm font-medium">
                    <i class="fas fa-fire text-xs mr-1"></i>人工智能
                </span>
                <span class="px-3 py-2 bg-blue-50 text-blue-600 rounded-lg text-sm font-medium">
                    <i class="fas fa-trending-up text-xs mr-1"></i>创业故事
                </span>
                <span class="px-3 py-2 bg-green-50 text-green-600 rounded-lg text-sm font-medium">
                    <i class="fas fa-heart text-xs mr-1"></i>心理健康
                </span>
                <span class="px-3 py-2 bg-purple-50 text-purple-600 rounded-lg text-sm font-medium">
                    <i class="fas fa-music text-xs mr-1"></i>音乐电台
                </span>
            </div>
        </div>

        <!-- 排行榜 -->
        <div class="px-4 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-bold text-gray-900">热门排行</h2>
                <button class="text-blue-500 text-sm font-medium">完整榜单</button>
            </div>
            <div class="space-y-3">
                <div class="flex items-center space-x-4 p-4 bg-white rounded-xl podcast-card">
                    <div class="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-sm">1</span>
                    </div>
                    <img src="https://images.unsplash.com/photo-1557804506-669a67965ba0?w=50&h=50&fit=crop&crop=center" 
                         alt="播客封面" class="w-12 h-12 rounded-lg object-cover">
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-900 text-sm">AI革命</h3>
                        <p class="text-gray-500 text-xs">科技前沿 · 150万播放</p>
                    </div>
                    <div class="text-right">
                        <div class="text-green-500 text-xs font-medium">
                            <i class="fas fa-arrow-up mr-1"></i>+5
                        </div>
                        <button class="mt-1 p-1.5 rounded-full bg-blue-50 text-blue-500">
                            <i class="fas fa-play text-xs"></i>
                        </button>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4 p-4 bg-white rounded-xl podcast-card">
                    <div class="w-8 h-8 bg-gradient-to-r from-gray-400 to-gray-600 rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-sm">2</span>
                    </div>
                    <img src="https://images.unsplash.com/photo-1551434678-e076c223a692?w=50&h=50&fit=crop&crop=center" 
                         alt="播客封面" class="w-12 h-12 rounded-lg object-cover">
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-900 text-sm">商业思维</h3>
                        <p class="text-gray-500 text-xs">商业洞察 · 128万播放</p>
                    </div>
                    <div class="text-right">
                        <div class="text-gray-500 text-xs font-medium">
                            <i class="fas fa-minus mr-1"></i>-
                        </div>
                        <button class="mt-1 p-1.5 rounded-full bg-blue-50 text-blue-500">
                            <i class="fas fa-play text-xs"></i>
                        </button>
                    </div>
                </div>

                <div class="flex items-center space-x-4 p-4 bg-white rounded-xl podcast-card">
                    <div class="w-8 h-8 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-sm">3</span>
                    </div>
                    <img src="https://images.unsplash.com/photo-1516035069371-29a1b244cc32?w=50&h=50&fit=crop&crop=center" 
                         alt="播客封面" class="w-12 h-12 rounded-lg object-cover">
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-900 text-sm">心理学漫步</h3>
                        <p class="text-gray-500 text-xs">心理健康 · 95万播放</p>
                    </div>
                    <div class="text-right">
                        <div class="text-red-500 text-xs font-medium">
                            <i class="fas fa-arrow-down mr-1"></i>-2
                        </div>
                        <button class="mt-1 p-1.5 rounded-full bg-blue-50 text-blue-500">
                            <i class="fas fa-play text-xs"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 推荐主播 -->
        <div class="px-4 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-bold text-gray-900">推荐主播</h2>
                <button class="text-blue-500 text-sm font-medium">查看更多</button>
            </div>
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-white rounded-xl p-4 text-center podcast-card">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=center" 
                         alt="主播头像" class="w-16 h-16 rounded-full object-cover mx-auto mb-3">
                    <h3 class="font-semibold text-gray-900 text-sm mb-1">张科技</h3>
                    <p class="text-gray-500 text-xs mb-3">科技评论员 · 50万关注</p>
                    <button class="w-full py-2 bg-blue-500 text-white rounded-lg text-sm font-medium">
                        <i class="fas fa-plus mr-1"></i>关注
                    </button>
                </div>
                <div class="bg-white rounded-xl p-4 text-center podcast-card">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b37c?w=60&h=60&fit=crop&crop=center" 
                         alt="主播头像" class="w-16 h-16 rounded-full object-cover mx-auto mb-3">
                    <h3 class="font-semibold text-gray-900 text-sm mb-1">李心理</h3>
                    <p class="text-gray-500 text-xs mb-3">心理咨询师 · 32万关注</p>
                    <button class="w-full py-2 bg-blue-500 text-white rounded-lg text-sm font-medium">
                        <i class="fas fa-plus mr-1"></i>关注
                    </button>
                </div>
            </div>
        </div>

        <!-- 分类专题 -->
        <div class="px-4 mb-6">
            <h2 class="text-lg font-bold text-gray-900 mb-4">分类专题</h2>
            <div class="space-y-4">
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-4 text-white relative overflow-hidden">
                    <h3 class="font-bold text-lg mb-2">科技前沿</h3>
                    <p class="text-sm opacity-90 mb-3">探索最新科技趋势和创新</p>
                    <div class="flex items-center justify-between">
                        <span class="text-xs opacity-80">156个播客</span>
                        <button class="px-4 py-2 bg-white bg-opacity-20 rounded-lg text-sm font-medium">
                            进入专题
                        </button>
                    </div>
                    <div class="absolute right-4 top-4 opacity-20">
                        <i class="fas fa-robot text-3xl"></i>
                    </div>
                </div>
                
                <div class="bg-gradient-to-r from-green-500 to-teal-600 rounded-xl p-4 text-white relative overflow-hidden">
                    <h3 class="font-bold text-lg mb-2">健康生活</h3>
                    <p class="text-sm opacity-90 mb-3">关注身心健康，提升生活品质</p>
                    <div class="flex items-center justify-between">
                        <span class="text-xs opacity-80">89个播客</span>
                        <button class="px-4 py-2 bg-white bg-opacity-20 rounded-lg text-sm font-medium">
                            进入专题
                        </button>
                    </div>
                    <div class="absolute right-4 top-4 opacity-20">
                        <i class="fas fa-heartbeat text-3xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部Tab Bar -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
        <div class="flex justify-around items-center">
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-home text-lg"></i>
                <span class="text-xs">首页</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-blue-500">
                <i class="fas fa-compass text-lg"></i>
                <span class="text-xs font-medium">发现</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-play-circle text-lg"></i>
                <span class="text-xs">播放</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-bookmark text-lg"></i>
                <span class="text-xs">我的</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-user text-lg"></i>
                <span class="text-xs">个人</span>
            </button>
        </div>
    </div>
</body>
</html>