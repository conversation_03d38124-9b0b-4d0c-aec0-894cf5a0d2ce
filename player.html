<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>播放器 - 播客宇宙</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .status-bar {
            background: linear-gradient(to right, #000, #111);
        }
        .player-bg {
            background: linear-gradient(180deg, 
                rgba(102, 126, 234, 0.1) 0%, 
                rgba(118, 75, 162, 0.05) 50%, 
                rgba(255, 255, 255, 1) 100%);
        }
        .control-btn {
            transition: all 0.3s ease;
        }
        .control-btn:hover {
            transform: scale(1.1);
        }
        .progress-bar {
            appearance: none;
            background: transparent;
            cursor: pointer;
        }
        .progress-bar::-webkit-slider-track {
            background: rgba(0,0,0,0.1);
            height: 4px;
            border-radius: 2px;
        }
        .progress-bar::-webkit-slider-thumb {
            appearance: none;
            height: 16px;
            width: 16px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }
        .playlist-item {
            transition: all 0.3s ease;
        }
        .playlist-item.active {
            background: rgba(102, 126, 234, 0.1);
            border-left: 3px solid #667eea;
        }
        .comment-item {
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
    </style>
</head>
<body class="player-bg font-sans">
    <!-- iOS状态栏 -->
    <div class="status-bar text-white text-sm flex justify-between items-center px-4 py-1 h-11">
        <div class="flex items-center space-x-2">
            <span class="font-medium">9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="w-6 h-3 border border-white rounded-sm relative">
                <div class="w-4 h-1.5 bg-white rounded-sm absolute top-0.5 left-0.5"></div>
            </div>
        </div>
    </div>

    <!-- 主要播放界面 -->
    <div class="flex flex-col h-screen pt-0 pb-20">
        <!-- 顶部控制栏 -->
        <div class="flex justify-between items-center p-4">
            <button class="p-2 rounded-full bg-white bg-opacity-20 backdrop-blur-sm">
                <i class="fas fa-chevron-down text-gray-700"></i>
            </button>
            <div class="text-center">
                <h3 class="text-sm font-medium text-gray-600">正在播放</h3>
                <p class="text-xs text-gray-500">科技前沿</p>
            </div>
            <button class="p-2 rounded-full bg-white bg-opacity-20 backdrop-blur-sm">
                <i class="fas fa-ellipsis-h text-gray-700"></i>
            </button>
        </div>

        <!-- 播客封面 -->
        <div class="flex-1 flex items-center justify-center px-8 py-6">
            <div class="relative">
                <img src="https://images.unsplash.com/photo-1557804506-669a67965ba0?w=280&h=280&fit=crop&crop=center" 
                     alt="播客封面" class="w-80 h-80 rounded-3xl object-cover shadow-2xl">
                <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-3xl"></div>
            </div>
        </div>

        <!-- 播客信息 -->
        <div class="px-6 py-4">
            <div class="flex justify-between items-start mb-4">
                <div class="flex-1">
                    <h1 class="text-xl font-bold text-gray-900 mb-1">人工智能的未来发展</h1>
                    <p class="text-gray-500 text-sm mb-2">科技前沿 · 第15期</p>
                    <p class="text-gray-400 text-xs">主播：张科技 · 2024年1月15日</p>
                </div>
                <div class="flex space-x-2">
                    <button class="p-2 rounded-full bg-gray-100 text-gray-600">
                        <i class="fas fa-heart"></i>
                    </button>
                    <button class="p-2 rounded-full bg-gray-100 text-gray-600">
                        <i class="fas fa-share"></i>
                    </button>
                </div>
            </div>

            <!-- 播放进度 -->
            <div class="mb-6">
                <input type="range" class="progress-bar w-full" value="35" max="100">
                <div class="flex justify-between text-xs text-gray-500 mt-2">
                    <span>12:35</span>
                    <span>35:42</span>
                </div>
            </div>

            <!-- 播放控制 -->
            <div class="flex items-center justify-center space-x-8 mb-6">
                <button class="control-btn p-3 text-gray-600">
                    <i class="fas fa-backward text-xl"></i>
                </button>
                <button class="control-btn p-4 bg-blue-500 text-white rounded-full shadow-lg">
                    <i class="fas fa-pause text-2xl"></i>
                </button>
                <button class="control-btn p-3 text-gray-600">
                    <i class="fas fa-forward text-xl"></i>
                </button>
            </div>

            <!-- 次要控制 -->
            <div class="flex items-center justify-between text-gray-500">
                <button class="flex items-center space-x-1 text-sm">
                    <i class="fas fa-redo text-xs"></i>
                    <span>15秒</span>
                </button>
                <button class="flex items-center space-x-1 text-sm bg-gray-100 px-3 py-1 rounded-full">
                    <i class="fas fa-tachometer-alt text-xs"></i>
                    <span>1.0x</span>
                </button>
                <button class="flex items-center space-x-1 text-sm">
                    <i class="fas fa-undo text-xs"></i>
                    <span>15秒</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 底部面板（播放列表和评论） -->
    <div class="fixed bottom-16 left-0 right-0 bg-white rounded-t-3xl shadow-2xl" style="height: 60vh; transform: translateY(calc(60vh - 60px));">
        <div class="p-4">
            <!-- 拖拽指示器 -->
            <div class="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-4"></div>
            
            <!-- 标签切换 -->
            <div class="flex space-x-8 mb-4">
                <button class="tab-btn text-blue-500 font-semibold border-b-2 border-blue-500 pb-2">
                    播放列表
                </button>
                <button class="tab-btn text-gray-500 pb-2">
                    评论 (156)
                </button>
            </div>

            <!-- 播放列表 -->
            <div class="space-y-2 max-h-96 overflow-y-auto">
                <div class="playlist-item active flex items-center space-x-3 p-3 rounded-lg">
                    <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-play text-white text-xs"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900 text-sm">人工智能的未来发展</h4>
                        <p class="text-gray-500 text-xs">35:42 · 正在播放</p>
                    </div>
                    <button class="text-gray-400">
                        <i class="fas fa-ellipsis-v text-sm"></i>
                    </button>
                </div>
                
                <div class="playlist-item flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50">
                    <div class="w-8 h-8 bg-gray-200 rounded-lg flex items-center justify-center">
                        <span class="text-gray-600 text-xs font-medium">14</span>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900 text-sm">区块链技术解析</h4>
                        <p class="text-gray-500 text-xs">42:15 · 未播放</p>
                    </div>
                    <button class="text-gray-400">
                        <i class="fas fa-ellipsis-v text-sm"></i>
                    </button>
                </div>

                <div class="playlist-item flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50">
                    <div class="w-8 h-8 bg-gray-200 rounded-lg flex items-center justify-center">
                        <span class="text-gray-600 text-xs font-medium">13</span>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900 text-sm">云计算趋势分析</h4>
                        <p class="text-gray-500 text-xs">38:30 · 已播放</p>
                    </div>
                    <button class="text-gray-400">
                        <i class="fas fa-ellipsis-v text-sm"></i>
                    </button>
                </div>

                <div class="playlist-item flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50">
                    <div class="w-8 h-8 bg-gray-200 rounded-lg flex items-center justify-center">
                        <span class="text-gray-600 text-xs font-medium">12</span>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900 text-sm">5G网络的商业应用</h4>
                        <p class="text-gray-500 text-xs">44:22 · 已播放</p>
                    </div>
                    <button class="text-gray-400">
                        <i class="fas fa-ellipsis-v text-sm"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部Tab Bar -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
        <div class="flex justify-around items-center">
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-home text-lg"></i>
                <span class="text-xs">首页</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-compass text-lg"></i>
                <span class="text-xs">发现</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-blue-500">
                <i class="fas fa-play-circle text-lg"></i>
                <span class="text-xs font-medium">播放</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-bookmark text-lg"></i>
                <span class="text-xs">我的</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-user text-lg"></i>
                <span class="text-xs">个人</span>
            </button>
        </div>
    </div>

    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 标签切换功能
            const tabBtns = document.querySelectorAll('.tab-btn');
            tabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    tabBtns.forEach(b => {
                        b.classList.remove('text-blue-500', 'font-semibold', 'border-b-2', 'border-blue-500');
                        b.classList.add('text-gray-500');
                    });
                    this.classList.add('text-blue-500', 'font-semibold', 'border-b-2', 'border-blue-500');
                    this.classList.remove('text-gray-500');
                });
            });

            // 播放按钮切换
            const playBtn = document.querySelector('.control-btn i.fa-pause');
            playBtn.parentElement.addEventListener('click', function() {
                if (playBtn.classList.contains('fa-pause')) {
                    playBtn.classList.remove('fa-pause');
                    playBtn.classList.add('fa-play');
                } else {
                    playBtn.classList.remove('fa-play');
                    playBtn.classList.add('fa-pause');
                }
            });
        });
    </script>
</body>
</html>