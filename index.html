<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>播客宇宙 - 高保真原型展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .phone-container {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            position: relative;
        }
        
        .phone-screen {
            width: 100%;
            height: 100%;
            border-radius: 32px;
            overflow: hidden;
            background: white;
        }
        
        .phone-iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 32px;
        }
        
        .header-section {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .page-label {
            position: absolute;
            top: -50px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 13px;
            font-weight: 600;
            color: #374151;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            white-space: nowrap;
        }
        
        .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(375px, 1fr));
            gap: 80px;
            max-width: 1600px;
            margin: 0 auto;
            padding: 60px 20px;
        }
        
        .phone-wrapper {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        @media (max-width: 768px) {
            .phone-container {
                width: 320px;
                height: 693px;
                border-radius: 32px;
                padding: 6px;
            }
            
            .phone-screen {
                border-radius: 26px;
            }
            
            .phone-iframe {
                border-radius: 26px;
            }
            
            .grid-container {
                grid-template-columns: 1fr;
                gap: 40px;
                padding: 20px 10px;
            }
        }
        
        .intro-text {
            color: white;
            text-align: center;
            margin-bottom: 40px;
        }
        
        .feature-badge {
            display: inline-block;
            background: rgba(255,255,255,0.9);
            color: #374151;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            margin: 4px;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="min-h-screen">
        <!-- 介绍区域 -->
        <div class="header-section mx-4 mt-8 p-8">
            <div class="text-center">
                <h1 class="text-3xl font-bold text-gray-900 mb-4">播客宇宙 App 原型</h1>
                <p class="text-gray-600 mb-6">类似小宇宙的播客应用高保真原型设计<br>模拟 iPhone 15 Pro 真实体验</p>
                
                <div class="flex flex-wrap justify-center mb-6">
                    <span class="feature-badge"><i class="fas fa-mobile-alt mr-1"></i>iPhone 15 Pro 适配</span>
                    <span class="feature-badge"><i class="fas fa-palette mr-1"></i>iOS 设计规范</span>
                    <span class="feature-badge"><i class="fas fa-images mr-1"></i>真实图片内容</span>
                    <span class="feature-badge"><i class="fas fa-code mr-1"></i>HTML + Tailwind CSS</span>
                    <span class="feature-badge"><i class="fas fa-mobile-alt mr-1"></i>响应式设计</span>
                </div>
                
                <div class="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                    <div class="bg-blue-50 p-3 rounded-xl">
                        <i class="fas fa-home text-blue-500 mb-2"></i>
                        <div class="font-semibold text-gray-900">首页</div>
                        <div class="text-gray-500 text-xs">推荐与发现</div>
                    </div>
                    <div class="bg-green-50 p-3 rounded-xl">
                        <i class="fas fa-compass text-green-500 mb-2"></i>
                        <div class="font-semibold text-gray-900">发现</div>
                        <div class="text-gray-500 text-xs">分类与搜索</div>
                    </div>
                    <div class="bg-purple-50 p-3 rounded-xl">
                        <i class="fas fa-play-circle text-purple-500 mb-2"></i>
                        <div class="font-semibold text-gray-900">播放器</div>
                        <div class="text-gray-500 text-xs">核心播放体验</div>
                    </div>
                    <div class="bg-orange-50 p-3 rounded-xl">
                        <i class="fas fa-bookmark text-orange-500 mb-2"></i>
                        <div class="font-semibold text-gray-900">我的</div>
                        <div class="text-gray-500 text-xs">订阅与收藏</div>
                    </div>
                    <div class="bg-red-50 p-3 rounded-xl">
                        <i class="fas fa-user text-red-500 mb-2"></i>
                        <div class="font-semibold text-gray-900">个人</div>
                        <div class="text-gray-500 text-xs">设置与账户</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 原型展示区域 -->
        <div class="grid-container">
            <!-- 首页 -->
            <div class="phone-wrapper">
                <div class="page-label">
                    <i class="fas fa-home mr-2"></i>首页 - 推荐与浏览
                </div>
                <div class="phone-container">
                    <div class="phone-screen">
                        <iframe src="home.html" class="phone-iframe" title="首页界面"></iframe>
                    </div>
                </div>
            </div>

            <!-- 发现页面 -->
            <div class="phone-wrapper">
                <div class="page-label">
                    <i class="fas fa-compass mr-2"></i>发现 - 分类与搜索
                </div>
                <div class="phone-container">
                    <div class="phone-screen">
                        <iframe src="discover.html" class="phone-iframe" title="发现界面"></iframe>
                    </div>
                </div>
            </div>

            <!-- 播放器页面 -->
            <div class="phone-wrapper">
                <div class="page-label">
                    <i class="fas fa-play-circle mr-2"></i>播放器 - 核心播放体验
                </div>
                <div class="phone-container">
                    <div class="phone-screen">
                        <iframe src="player.html" class="phone-iframe" title="播放器界面"></iframe>
                    </div>
                </div>
            </div>

            <!-- 我的播客页面 -->
            <div class="phone-wrapper">
                <div class="page-label">
                    <i class="fas fa-bookmark mr-2"></i>我的播客 - 订阅与收藏
                </div>
                <div class="phone-container">
                    <div class="phone-screen">
                        <iframe src="library.html" class="phone-iframe" title="我的播客界面"></iframe>
                    </div>
                </div>
            </div>

            <!-- 个人资料页面 -->
            <div class="phone-wrapper">
                <div class="page-label">
                    <i class="fas fa-user mr-2"></i>个人资料 - 设置与账户
                </div>
                <div class="phone-container">
                    <div class="phone-screen">
                        <iframe src="profile.html" class="phone-iframe" title="个人资料界面"></iframe>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部信息 -->
        <div class="text-center py-12">
            <div class="header-section inline-block p-6">
                <h3 class="text-xl font-bold text-gray-900 mb-4">技术特性</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                    <div>
                        <i class="fas fa-code text-blue-500 text-2xl mb-3"></i>
                        <h4 class="font-semibold text-gray-900 mb-2">现代化技术栈</h4>
                        <p class="text-gray-600">HTML5 + Tailwind CSS + Font Awesome<br>响应式设计，跨设备兼容</p>
                    </div>
                    <div>
                        <i class="fas fa-mobile-alt text-green-500 text-2xl mb-3"></i>
                        <h4 class="font-semibold text-gray-900 mb-2">真实移动体验</h4>
                        <p class="text-gray-600">模拟 iPhone 15 Pro 界面<br>iOS 设计规范，原生交互体验</p>
                    </div>
                    <div>
                        <i class="fas fa-rocket text-purple-500 text-2xl mb-3"></i>
                        <h4 class="font-semibold text-gray-900 mb-2">即用型原型</h4>
                        <p class="text-gray-600">高保真界面设计<br>直接用于开发和演示</p>
                    </div>
                </div>
                
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <p class="text-gray-500 text-sm">
                        <i class="fas fa-copyright mr-1"></i>2024 播客宇宙原型 · 
                        <i class="fas fa-code mr-1"></i>Generated with Claude Code
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为手机容器添加悬停效果
            const phoneContainers = document.querySelectorAll('.phone-container');
            
            phoneContainers.forEach(container => {
                container.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.02)';
                    this.style.transition = 'transform 0.3s ease';
                });
                
                container.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
            
            // 动态加载效果
            const phoneWrappers = document.querySelectorAll('.phone-wrapper');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, { threshold: 0.1 });
            
            phoneWrappers.forEach(wrapper => {
                wrapper.style.opacity = '0';
                wrapper.style.transform = 'translateY(50px)';
                wrapper.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(wrapper);
            });
        });
    </script>
</body>
</html>