# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Chinese podcast app prototype called "播客宇宙" (Podcast Universe) - a high-fidelity mobile app prototype similar to Xiaoyuzhou (Little Universe). The prototype is built as a collection of HTML pages that simulate an iPhone 15 Pro experience with iOS design standards.

## Architecture

- **Static HTML Prototype**: Five main HTML pages representing different app screens
- **Responsive Design**: Mobile-first approach with iPhone 15 Pro specifications (375px × 812px)
- **Technology Stack**: HTML5, Tailwind CSS, Font Awesome icons
- **Design System**: iOS-style interface with consistent gradient themes and card-based layouts

## File Structure

- `index.html` - Main showcase page displaying all prototype screens in phone containers
- `home.html` - Home/main feed page with recommended podcasts and recent plays  
- `discover.html` - Discovery page with categories, trending, and search functionality
- `player.html` - Audio player interface with playback controls and playlist
- `library.html` - User's podcast subscriptions and library management
- `profile.html` - User profile and app settings

## Key Design Patterns

**Common UI Components:**
- iOS status bar simulation (time: 9:41, signal/wifi/battery indicators)
- Bottom tab bar navigation (5 tabs: 首页/发现/播放/我的/个人)
- Gradient backgrounds: `linear-gradient(135deg, #667eea 0%, #764ba2 100%)`
- Card-based layouts with hover animations and shadows
- Rounded corners (12px-24px) following iOS design language

**Interactive Elements:**
- Hover effects on cards (transform: translateY(-2px))
- Tab switching functionality with JavaScript
- Toggle switches for settings (custom CSS styling)
- Progress bars for audio playback

## Development Guidelines

When working with this codebase:

1. **Maintain iOS Design Consistency**: Follow the established gradient color scheme and rounded corner patterns
2. **Preserve Mobile Layout**: All screens are designed for 375px width with fixed bottom navigation
3. **Use Existing Component Patterns**: Reuse card layouts, button styles, and spacing from existing pages
4. **Keep Chinese Language**: All UI text is in Chinese - maintain this for consistency
5. **External Dependencies**: Uses CDN for Tailwind CSS and Font Awesome - no local builds required

## Content Management

- **Images**: All images use Unsplash URLs with specific dimensions and cropping
- **Content**: Podcast data, user names, and statistics are hardcoded placeholder content
- **Typography**: Uses system fonts with Chinese language optimization

This is a prototype project focused on visual design demonstration rather than functional backend integration.